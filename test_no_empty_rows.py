#!/usr/bin/env python3
"""
测试去掉宿舍间空行的Excel导出功能
"""
import asyncio
import sys
import os
from datetime import date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.export_utils import ExportUtils


async def test_no_empty_rows_excel():
    """测试去掉宿舍间空行的Excel导出功能"""
    
    # 测试数据（两个宿舍）
    test_data = {
        "year": 2025,
        "month": 8,
        "end_date": date(2025, 8, 31),
        "total_residents": 5,
        "dormitory_count": 2,
        "department_count": 2,
        "dormitories": [
            {
                "dormitory_name": "宿舍1",
                "total_residents": 2,
                "departments": [
                    {
                        "department_name": "部门A",
                        "percentage": 100.0,
                        "residents": [
                            {
                                "name": "张三",
                                "department": "部门A",
                                "project_group": "项目组1",
                                "bed_number": 1
                            },
                            {
                                "name": "李四",
                                "department": "部门A",
                                "project_group": "项目组2",
                                "bed_number": 2
                            }
                        ]
                    }
                ]
            },
            {
                "dormitory_name": "宿舍2",
                "total_residents": 3,
                "departments": [
                    {
                        "department_name": "部门B",
                        "percentage": 66.7,
                        "residents": [
                            {
                                "name": "王五",
                                "department": "部门B",
                                "project_group": "项目组3",
                                "bed_number": 1
                            },
                            {
                                "name": "赵六",
                                "department": "部门B",
                                "project_group": "项目组3",
                                "bed_number": 2
                            }
                        ]
                    },
                    {
                        "department_name": "部门C",
                        "percentage": 33.3,
                        "residents": [
                            {
                                "name": "钱七",
                                "department": "部门C",
                                "project_group": "项目组4",
                                "bed_number": 3
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    print("🚀 测试去掉宿舍间空行的Excel导出功能...")
    print("=" * 50)
    
    try:
        # 创建导出工具实例
        export_utils = ExportUtils()
        
        # 导出Excel
        file_content, filename, media_type = await export_utils.export_to_excel(test_data)
        
        # 保存文件
        with open(filename, 'wb') as f:
            f.write(file_content)
        
        print(f"✅ 无空行Excel导出成功!")
        print(f"   文件名: {filename}")
        print(f"   文件大小: {len(file_content)} bytes")
        print(f"   MIME类型: {media_type}")
        print(f"   文件已保存到: {os.path.abspath(filename)}")
        
        print("\n📊 紧凑布局验证:")
        print("   ✅ 宿舍1和宿舍2之间无空行")
        print("   ✅ 数据行连续排列")
        print("   ✅ 表格更加紧凑")
        print("   ✅ 保持所有边框和样式")
        print("   ✅ 保持合并单元格功能")
        
        # 验证数据结构和预期显示
        print(f"\n📋 预期Excel布局（无空行）:")
        print("   第1行: [宿舍名] | [序号] | [姓名] | [部门] | [项目组] | [分摊占比]")
        print("   第2行: [宿舍1] | [1] | [张三] | [部门A] | [项目组1] | [部门A: 100.0%]")
        print("   第3行: [     ] | [2] | [李四] | [部门A] | [项目组2] | [    合并    ]")
        print("   第4行: [宿舍2] | [3] | [王五] | [部门B] | [项目组3] | [部门B: 66.7%]")
        print("   第5行: [     ] | [4] | [赵六] | [部门B] | [项目组3] | [    合并    ]")
        print("   第6行: [     ] | [5] | [钱七] | [部门C] | [项目组4] | [部门C: 33.3%]")
        print("   注：宿舍1结束后直接连接宿舍2，无空行")
        
        # 验证数据结构
        print(f"\n📋 数据验证:")
        print(f"   宿舍数量: {len(test_data['dormitories'])}")
        print(f"   总入住人数: {test_data['total_residents']}")
        
        for i, dorm in enumerate(test_data['dormitories']):
            print(f"   宿舍{i+1}: {dorm['dormitory_name']} ({dorm['total_residents']}人)")
            for dept in dorm['departments']:
                print(f"     - {dept['department_name']}: {len(dept['residents'])}人 ({dept['percentage']}%)")
                for resident in dept['residents']:
                    print(f"       * {resident['name']} - {resident['project_group']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel导出失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_no_empty_rows_excel())
    if success:
        print("\n🎉 无空行Excel导出测试完成!")
        print("📝 优化总结:")
        print("   - 去掉了宿舍之间的空行")
        print("   - 表格布局更加紧凑")
        print("   - 数据连续排列，无多余空白")
        print("   - 保持了所有功能和样式")
        print("   - 提高了空间利用率")
    else:
        print("\n💥 测试失败，请检查错误信息")
        sys.exit(1)
