"""
分摊占比计算服务
"""
from typing import Dict, List, Any
from datetime import date
import calendar
from sqlalchemy.orm import Session

from app.services.report_service import ReportService
from app.core.logging import get_logger

logger = get_logger(__name__)


class AllocationPercentageService:
    """分摊占比计算服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.report_service = ReportService(db)
    
    async def calculate_monthly_allocation_percentages(
        self,
        year: int,
        month: int,
        end_date: date = None
    ) -> Dict[str, Any]:
        """
        计算月度分摊占比（统一的计算逻辑）
        
        Args:
            year: 年份
            month: 月份
            end_date: 截止日期
            
        Returns:
            Dict: 包含分摊占比的报告数据
        """
        logger.info(f"开始计算月度分摊占比: {year}年{month}月, 截止日期: {end_date}")
        
        # 1. 获取基础月度报告数据
        report = await self.report_service.get_monthly_report(year, month, end_date)
        
        # 2. 按宿舍分组统计床位天数
        dormitory_allocations = []
        
        for detail in report.daily_details:
            # 查找是否已存在该宿舍
            dorm_found = False
            for dorm_alloc in dormitory_allocations:
                if dorm_alloc["dormitory_name"] == detail.dormitory_name:
                    # 累加该宿舍的数据
                    dorm_alloc["total_bed_days"] += detail.total_beds
                    for dept_alloc in detail.department_allocations:
                        dept_id = dept_alloc["department_id"]
                        if dept_id not in dorm_alloc["departments"]:
                            dorm_alloc["departments"][dept_id] = {
                                "department_name": dept_alloc["department_name"],
                                "bed_days": 0
                            }
                        dorm_alloc["departments"][dept_id]["bed_days"] += detail.total_beds * dept_alloc["allocation_ratio"]
                    dorm_found = True
                    break
            
            if not dorm_found:
                # 新宿舍
                departments = {}
                for dept_alloc in detail.department_allocations:
                    dept_id = dept_alloc["department_id"]
                    departments[dept_id] = {
                        "department_name": dept_alloc["department_name"],
                        "bed_days": detail.total_beds * dept_alloc["allocation_ratio"]
                    }
                
                dormitory_allocations.append({
                    "dormitory_name": detail.dormitory_name,
                    "total_bed_days": detail.total_beds,
                    "allocation_percentage": 0.0,  # 稍后计算
                    "departments": departments
                })
        
        # 3. 计算总床位天数和宿舍分摊百分比
        total_bed_days = sum(dorm["total_bed_days"] for dorm in dormitory_allocations)
        for dorm_alloc in dormitory_allocations:
            if total_bed_days > 0:
                dorm_alloc["allocation_percentage"] = round((dorm_alloc["total_bed_days"] / total_bed_days) * 100, 1)
            
            # 4. 计算宿舍内各部门占比
            dorm_total_days = dorm_alloc["total_bed_days"]
            for dept_info in dorm_alloc["departments"].values():
                if dorm_total_days > 0:
                    dept_info["percentage_in_dorm"] = round((dept_info["bed_days"] / dorm_total_days) * 100, 1)
        
        # 5. 计算统计日期范围
        start_date = date(year, month, 1)
        month_last_day = date(year, month, calendar.monthrange(year, month)[1])
        today = date.today()
        
        if end_date:
            actual_end_date = min(end_date, month_last_day)
        else:
            if year == today.year and month == today.month and today.day > 1:
                actual_end_date = date(year, month, today.day - 1)
            else:
                actual_end_date = month_last_day
        
        return {
            "dormitory_allocations": dormitory_allocations,
            "year": year,
            "month": month,
            "start_date": start_date,
            "end_date": actual_end_date,
            "is_current_month": year == date.today().year and month == date.today().month,
            "total_bed_days": total_bed_days
        }
    
    def convert_to_export_format(self, allocation_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将分摊占比数据转换为Excel导出格式
        
        Args:
            allocation_data: 分摊占比计算结果
            
        Returns:
            Dict: Excel导出格式的数据
        """
        dormitories = []
        total_residents = 0
        department_names = set()
        
        for dorm_alloc in allocation_data["dormitory_allocations"]:
            dorm_name = dorm_alloc["dormitory_name"]
            departments = []
            dorm_total_residents = 0
            
            for dept_id, dept_info in dorm_alloc["departments"].items():
                dept_name = dept_info["department_name"]
                percentage = dept_info.get("percentage_in_dorm", 0)
                department_names.add(dept_name)
                
                # 这里需要获取实际的住户信息
                # 为了保持一致性，我们需要从daily_details中提取住户信息
                residents = self._extract_residents_for_department(
                    dorm_name, dept_name, allocation_data
                )
                
                if residents:
                    departments.append({
                        "department_name": dept_name,
                        "percentage": percentage,
                        "residents": residents
                    })
                    dorm_total_residents += len(residents)
            
            if departments:
                dormitories.append({
                    "dormitory_name": dorm_name,
                    "total_residents": dorm_total_residents,
                    "departments": departments
                })
                total_residents += dorm_total_residents
        
        return {
            "year": allocation_data["year"],
            "month": allocation_data["month"],
            "end_date": allocation_data["end_date"],
            "total_residents": total_residents,
            "dormitory_count": len(dormitories),
            "department_count": len(department_names),
            "dormitories": dormitories
        }
    
    def _extract_residents_for_department(
        self,
        dorm_name: str,
        dept_name: str,
        allocation_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        从数据库查询特定宿舍和部门的住户信息
        """
        from app.repositories.record_repo import RecordRepository
        from app.repositories.dormitory_repo import DormitoryRepository

        try:
            record_repo = RecordRepository(self.db)
            dorm_repo = DormitoryRepository(self.db)

            # 获取宿舍ID
            dormitory = dorm_repo.get_by_name(dorm_name)
            if not dormitory:
                return []

            # 获取活跃的入住记录
            active_records = record_repo.get_active_records()

            # 筛选出该宿舍和部门的住户
            residents = []
            for record in active_records:
                if (record.dormitory_id == dormitory.id and
                    record.resident and
                    record.resident.department and
                    record.resident.department.name == dept_name):

                    residents.append({
                        "name": record.resident.name,
                        "department": record.resident.department.name,
                        "project_group": record.project_group,
                        "bed_number": record.bed_number
                    })

            # 按床位号排序
            residents.sort(key=lambda x: x["bed_number"])
            return residents

        except Exception as e:
            logger.error(f"提取住户信息失败: {str(e)}")
            return []
