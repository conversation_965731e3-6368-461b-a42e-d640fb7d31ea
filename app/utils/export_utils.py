"""
导出工具类
"""
import io
from typing import Tuple, Any, Dict, List
from datetime import date
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont


class ExportUtils:
    """导出工具类"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        # 尝试注册中文字体，如果失败则使用默认字体
        try:
            # 这里可以根据系统情况调整字体路径
            # pdfmetrics.registerFont(TTFont('SimHei', 'SimHei.ttf'))
            pass
        except:
            pass
    
    async def export_to_excel(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出Excel格式报告（新格式：宿舍-人员表格布局）

        Args:
            report_data: 报告数据

        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        from openpyxl.styles import PatternFill, Border, Side

        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "月度报告"

        # 添加标题行
        ws['A1'] = "宿舍名"
        ws['B1'] = "序号"
        ws['C1'] = "姓名"
        ws['D1'] = "部门"
        ws['E1'] = "项目组"
        ws['F1'] = "分摊占比"

        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 设置标题行样式（包括F列和边框）
        for col in ['A', 'B', 'C', 'D', 'E', 'F']:
            header_cell = ws[f'{col}1']
            header_cell.font = Font(bold=True)
            header_cell.alignment = Alignment(horizontal='center')
            header_cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
            header_cell.border = thin_border

        # 开始绘制宿舍数据，从第二行开始
        row = 2

        for dorm_index, dormitory in enumerate(report_data.get('dormitories', [])):
            dorm_name = dormitory['dormitory_name']
            departments = dormitory['departments']
            total_residents = dormitory['total_residents']


            # 计算需要的行数（所有住户数量）
            total_resident_rows = sum(len(dept['residents']) for dept in departments)

            if total_resident_rows == 0:
                continue

            # 宿舍名称区域（左侧，合并单元格）
            start_row = row
            end_row = row + total_resident_rows - 1

            # 合并宿舍名称单元格
            ws.merge_cells(f'A{start_row}:A{end_row}')
            dorm_cell = ws[f'A{start_row}']
            dorm_cell.value = dorm_name
            dorm_cell.font = Font(size=12, bold=True)
            dorm_cell.alignment = Alignment(horizontal='center', vertical='center')
            dorm_cell.border = thin_border

            # 人员数据（直接开始，不需要重复表头）
            current_row = start_row
            sequence_num = 1

            for dept in departments:
                dept_name = dept['department_name']
                percentage = dept['percentage']
                residents = dept['residents']

                # 记录部门开始和结束行，用于合并单元格
                dept_start_row = current_row
                dept_end_row = current_row + len(residents) - 1

                for i, resident in enumerate(residents):
                    # 序号
                    seq_cell = ws[f'B{current_row}']
                    seq_cell.value = sequence_num
                    seq_cell.alignment = Alignment(horizontal='center')
                    seq_cell.border = thin_border

                    # 姓名
                    name_cell = ws[f'C{current_row}']
                    name_cell.value = resident['name']
                    name_cell.alignment = Alignment(horizontal='center')
                    name_cell.border = thin_border

                    # 部门
                    dept_cell = ws[f'D{current_row}']
                    dept_cell.value = resident['department']
                    dept_cell.alignment = Alignment(horizontal='center')
                    dept_cell.border = thin_border

                    # 项目组
                    project_cell = ws[f'E{current_row}']
                    project_cell.value = resident['project_group']
                    project_cell.alignment = Alignment(horizontal='center')
                    project_cell.border = thin_border

                    sequence_num += 1
                    current_row += 1

                # 设置部门占比和边框
                if percentage > 0:
                    stats_text = f"{dept_name}: {percentage}%"

                    if len(residents) > 1:
                        # 多行部门：合并单元格
                        ws.merge_cells(f'F{dept_start_row}:F{dept_end_row}')
                        stats_cell = ws[f'F{dept_start_row}']
                        stats_cell.value = stats_text
                        stats_cell.font = Font(size=10)
                        stats_cell.alignment = Alignment(horizontal='center', vertical='center')
                        stats_cell.border = thin_border

                        # 为合并单元格范围内的所有单元格设置边框
                        for r in range(dept_start_row, dept_end_row + 1):
                            cell = ws[f'F{r}']
                            cell.border = thin_border
                    else:
                        # 单行部门：直接设置
                        stats_cell = ws[f'F{dept_start_row}']
                        stats_cell.value = stats_text
                        stats_cell.font = Font(size=10)
                        stats_cell.alignment = Alignment(horizontal='center', vertical='center')
                        stats_cell.border = thin_border

            # 更新行位置，直接连接下一个宿舍（无空行）
            row = current_row

        # 设置列宽（紧凑布局，无空列）
        ws.column_dimensions['A'].width = 20  # 宿舍名称
        ws.column_dimensions['B'].width = 8   # 序号
        ws.column_dimensions['C'].width = 12  # 姓名
        ws.column_dimensions['D'].width = 15  # 部门
        ws.column_dimensions['E'].width = 15  # 项目组
        ws.column_dimensions['F'].width = 20  # 统计信息

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.xlsx"
        return output.getvalue(), filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    
    async def export_to_csv(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出CSV格式报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        # 准备数据
        rows = []
        
        # 添加标题行
        rows.append([f"{report_data['year']}年{report_data['month']}月宿舍入住情况报告"])
        rows.append([])  # 空行

        # 基本信息
        rows.append(['报告日期', str(report_data.get('end_date', ''))])
        rows.append(['总入住人数', report_data.get('total_residents', 0)])
        rows.append(['部门数量', report_data.get('department_count', 0)])
        rows.append(['宿舍数量', report_data.get('dormitory_count', 0)])
        rows.append([])  # 空行

        # 按宿舍导出数据
        for dormitory in report_data.get('dormitories', []):
            dorm_name = dormitory['dormitory_name']
            departments = dormitory['departments']

            # 宿舍标题
            rows.append([f"=== {dorm_name} ==="])
            rows.append(['序号', '姓名', '部门', '项目组', '床位号'])

            # 人员数据
            sequence_num = 1
            for dept in departments:
                for resident in dept['residents']:
                    rows.append([
                        sequence_num,
                        resident['name'],
                        resident['department'],
                        resident['project_group'],
                        resident['bed_number']
                    ])
                    sequence_num += 1

            # 部门统计
            rows.append([])  # 空行
            rows.append(['部门统计:'])
            for dept in departments:
                if dept['percentage'] > 0:
                    rows.append([f"{dept['department_name']}: {dept['percentage']}%"])

            rows.append([])  # 空行分隔
        
        # 转换为DataFrame并导出CSV
        df = pd.DataFrame(rows)
        output = io.StringIO()
        df.to_csv(output, index=False, header=False, encoding='utf-8-sig')
        
        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.csv"
        return output.getvalue().encode('utf-8-sig'), filename, "text/csv"

    async def export_to_pdf(self, report_data: Dict[str, Any]) -> Tuple[bytes, str, str]:
        """
        导出PDF格式报告

        Args:
            report_data: 报告数据

        Returns:
            Tuple[bytes, str, str]: (文件内容, 文件名, MIME类型)
        """
        output = io.BytesIO()
        doc = SimpleDocTemplate(output, pagesize=A4)
        story = []

        # 标题
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # 居中
        )
        title = f"{report_data['year']}年{report_data['month']}月宿舍入住情况报告"
        story.append(Paragraph(title, title_style))

        # 基本信息
        info_data = [
            ['报告日期', str(report_data.get('end_date', '')), '总入住人数', str(report_data.get('total_residents', 0))],
            ['部门数量', str(report_data.get('department_count', 0)), '宿舍数量', str(report_data.get('dormitory_count', 0))]
        ]

        info_table = Table(info_data, colWidths=[2*inch, 1.5*inch, 2*inch, 1.5*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 0), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(info_table)
        story.append(Spacer(1, 20))

        # 按宿舍显示入住情况
        for dormitory in report_data.get('dormitories', []):
            dorm_name = dormitory['dormitory_name']
            departments = dormitory['departments']

            # 宿舍标题
            dorm_title_style = ParagraphStyle(
                'DormTitle',
                parent=self.styles['Heading2'],
                fontSize=14,
                spaceAfter=10,
                spaceBefore=20
            )
            story.append(Paragraph(f"宿舍：{dorm_name}", dorm_title_style))

            # 人员表格
            table_data = [['序号', '姓名', '部门', '项目组']]

            sequence_num = 1
            for dept in departments:
                for resident in dept['residents']:
                    table_data.append([
                        str(sequence_num),
                        resident['name'],
                        resident['department'],
                        resident['project_group']
                    ])
                    sequence_num += 1

            # 创建人员表格
            resident_table = Table(table_data, colWidths=[0.8*inch, 1.5*inch, 2*inch, 2*inch])
            resident_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(resident_table)

            # 部门统计
            stats_text = "部门占比：" + "，".join([f"{dept['department_name']}: {dept['percentage']}%"
                                                for dept in departments if dept['percentage'] > 0])
            stats_style = ParagraphStyle(
                'Stats',
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=10
            )
            story.append(Paragraph(stats_text, stats_style))

        # 构建PDF
        doc.build(story)
        output.seek(0)

        filename = f"月度报告_{report_data['year']}年{report_data['month']}月.pdf"
        return output.getvalue(), filename, "application/pdf"
